# Swap Approval Process Test Suite

## Overview

This document describes the comprehensive test suite for the swap approval process workflow. The tests verify the complete end-to-end flow from user swap request creation to admin approval, including all balance changes, transaction logging, and pool reserve updates.

## Swap Approval Workflow

### Complete Process Flow

1. **User Request Phase**
   - User creates swap request (MVT → USDC)
   - MVT tokens are locked in user balance
   - Swap request stored with PENDING status

2. **Admin Approval Phase**
   - Admin reviews swap request
   - Admin approves swap request
   - System executes approval workflow

3. **Execution Phase**
   - USDC transferred to user wallet via blockchain
   - Locked MVT tokens transferred to central pool
   - Transaction records created for audit trail
   - Pool reserves updated (MVT+, USDC-)
   - Swap request marked as APPROVED

## Test Implementation

### Test File Location
```
__tests__/scenarios/swap-approval-process.test.js
```

### Test Runner Script
```bash
npm run test:swap-approval
```

Or directly:
```bash
node test-swap-approval.js
```

## Test Coverage

### ✅ Happy Path Tests

#### 1. Complete Swap Approval Workflow
- **Description**: Tests the entire swap approval process end-to-end
- **Verifies**:
  - USDC transfer to user wallet
  - MVT transfer from user to central pool
  - Swap request status update to APPROVED
  - Transaction records creation (MVT and USDC)
  - Admin approval tracking

#### 2. Balance Changes Verification
- **Description**: Verifies all balance changes during swap approval
- **Verifies**:
  - User MVT balance decrease
  - Central MVT pool balance increase
  - USDC pool balance decrease
  - Balance consistency and atomicity

#### 3. Transaction Logging Audit Trail
- **Description**: Ensures complete transaction logging
- **Verifies**:
  - MVT transaction record creation
  - USDC transaction record creation
  - Transaction metadata completeness
  - Blockchain transaction hash recording

#### 4. Pool Reserve Updates
- **Description**: Validates pool reserve balance updates
- **Verifies**:
  - MVT addition to central pool
  - USDC deduction from liquidity pool
  - Reserve balance consistency
  - Pool state integrity

### ❌ Error Handling Tests

#### 1. Non-existent Swap Request
- **Description**: Tests handling of invalid swap request IDs
- **Expected**: 500 error with "not found" message

#### 2. Already Processed Swap Request
- **Description**: Tests prevention of duplicate processing
- **Expected**: 500 error with "already processed" message

#### 3. USDC Transfer Failure
- **Description**: Tests blockchain transfer failure handling
- **Expected**: 500 error with proper error propagation

#### 4. MVT Transfer Failure with Rollback
- **Description**: Tests MVT transfer failure and rollback mechanisms
- **Expected**: 500 error with attempted rollback verification

#### 5. Non-admin Authorization
- **Description**: Tests security enforcement for admin-only operations
- **Expected**: 401 Unauthorized error

#### 6. Insufficient USDC Liquidity Pool
- **Description**: Tests handling of insufficient USDC reserves
- **Expected**: 500 error with liquidity pool message

## Financial Operations Tested

### Balance Movements

| Operation | From | To | Token | Verification |
|-----------|------|----|---------|----|
| Release Locked | User Locked Balance | User Available Balance | MVT | ✅ |
| Transfer to Pool | User Available Balance | Central MVT Pool | MVT | ✅ |
| Transfer to User | USDC Liquidity Pool | User Wallet Address | USDC | ✅ |

### Exchange Rate Calculations
- MVT to USDC conversion using admin-defined rates
- Real-time rate validation
- Conversion amount accuracy

### Pool Reserve Updates
- **MVT Pool**: Increase by swap amount
- **USDC Pool**: Decrease by converted amount
- **Balance Verification**: Before/after state consistency

## Audit Trail Components

### Transaction Records

#### MVT Transaction
```javascript
{
  transactionType: "MVT_SWAP",
  tokenType: "MVT",
  amount: mvtAmount,
  fromWalletId: `user-wallet-${userId}`,
  toWalletId: "central-mvt-wallet",
  status: "COMPLETED",
  swapRequestId: swapRequestId,
  adminUserId: adminUserId
}
```

#### USDC Transaction
```javascript
{
  transactionType: "USDC_SWAP",
  tokenType: "USDC",
  amount: usdcAmount,
  fromWalletId: "usdc-liquidity-pool",
  toWalletId: `user-wallet-${userId}`,
  status: "COMPLETED",
  transactionHash: blockchainTxHash,
  swapRequestId: swapRequestId,
  adminUserId: adminUserId
}
```

### Swap Request Updates
- Status: PENDING → APPROVED
- Admin approval timestamp
- Admin user ID tracking
- Blockchain transaction hash
- Processing metadata

## Security Features Tested

### Authorization
- ✅ Admin-only access enforcement
- ✅ User ID validation
- ✅ Swap request ownership verification

### Data Integrity
- ✅ Atomic transaction operations
- ✅ Balance consistency checks
- ✅ State transition validation
- ✅ Duplicate processing prevention

### Error Recovery
- ✅ Blockchain failure handling
- ✅ Database rollback mechanisms
- ✅ Partial failure recovery
- ✅ Audit trail preservation

## Integration Points

### Services Tested
- **Swap Service**: Core approval logic
- **Wallet Service**: MVT balance management
- **USDC Service**: USDC transfer operations
- **Exchange Rate Service**: Rate calculations
- **Transaction Service**: Audit logging
- **Auth Service**: Admin authorization

### External Dependencies
- **Blockchain Contracts**: USDC transfer execution
- **DynamoDB**: Data persistence
- **AWS Services**: Infrastructure components

## Performance Considerations

### Test Execution
- **Parallel Execution**: Independent test isolation
- **Mock Optimization**: Minimal external dependencies
- **Memory Management**: Proper cleanup between tests

### Production Readiness
- **Atomic Operations**: Database transaction safety
- **Error Boundaries**: Graceful failure handling
- **Monitoring Points**: Comprehensive logging

## Usage Examples

### Running Specific Test Categories

```bash
# Run all swap approval tests
npm run test:swap-approval

# Run with coverage
npm run test:swap-approval -- --coverage

# Run specific test pattern
npx jest swap-approval-process --testNamePattern="should successfully complete"
```

### Integration with CI/CD

```yaml
# Example GitHub Actions step
- name: Test Swap Approval Process
  run: npm run test:swap-approval
  env:
    NODE_ENV: test
```

## Troubleshooting

### Common Issues

1. **Mock Setup Errors**
   - Verify all service mocks are properly configured
   - Check mock return values match expected formats

2. **Async Operation Failures**
   - Ensure proper await/async handling
   - Verify Promise resolution in mocks

3. **Balance Calculation Errors**
   - Check exchange rate mock values
   - Verify amount calculations in tests

### Debug Commands

```bash
# Run with verbose output
npm run test:swap-approval -- --verbose

# Run single test with debugging
npx jest swap-approval-process --testNamePattern="specific test" --verbose
```

## Future Enhancements

### Additional Test Scenarios
1. **Concurrent Approval Handling**
2. **Rate Limit Testing**
3. **Large Amount Processing**
4. **Network Failure Recovery**
5. **Database Consistency Validation**

### Performance Testing
1. **Load Testing**: Multiple concurrent approvals
2. **Stress Testing**: High-volume scenarios
3. **Memory Testing**: Long-running operations

## Related Documentation

- [Admin USDC Transfer Tests](./ADMIN_USDC_TRANSFER.md)
- [User Journey Tests](./USER_JOURNEY.md)
- [Data Integrity Tests](./DATA_INTEGRITY.md)
- [Error Recovery Tests](./ERROR_RECOVERY.md)
