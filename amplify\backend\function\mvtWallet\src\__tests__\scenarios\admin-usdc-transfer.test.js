/**
 * Admin USDC Transfer Scenario Test Suite
 * Tests for admin transferring USDC from liquidity pool to user wallet
 */

// Create mock DynamoDB instance
const mockDynamoDB = {
  getItem: jest.fn(),
  putItem: jest.fn(),
  updateItem: jest.fn(),
  scan: jest.fn(),
  query: jest.fn(),
  describeTable: jest.fn(),
  batchGetItem: jest.fn(),
  batchWriteItem: jest.fn()
};

// Mock all dependencies
jest.mock('../../config/aws', () => ({
  AWS: {
    DynamoDB: {
      Converter: {
        marshall: jest.fn((item) => item),
        unmarshall: jest.fn((item) => item)
      }
    }
  },
  ddb: mockDynamoDB
}));

const mockDynamoUtils = {
  getTableName: jest.fn((tableName) => `test-${tableName}`),
  tableExists: jest.fn().mockResolvedValue(true)
};

jest.mock('../../shared/database/dynamoUtils', () => mockDynamoUtils);

jest.mock('../../shared/services/authService', () => ({
  checkAdminAuthorization: jest.fn(),
  getCurrentUserDatabaseId: jest.fn(),
  getUserIdFromEvent: jest.fn(),
  checkUserAuthorization: jest.fn()
}));

jest.mock('../../shared/utils/validationUtils', () => ({
  validateUSDCTransferInput: jest.fn().mockReturnValue({ isValid: true }),
  validateUSDCAmount: jest.fn().mockReturnValue({ isValid: true }),
  validateUserId: jest.fn().mockReturnValue({ isValid: true })
}));

jest.mock('../../modules/transaction/transaction.service', () => ({
  generateTransactionId: jest.fn((prefix) => `${prefix}-${Date.now()}-test123`),
  createMVTWalletTransaction: jest.fn().mockResolvedValue(true)
}));

// Mock the USDC service with a spy that we can override per test
const mockUSDCService = {
  getUSDCLiquidityPool: jest.fn().mockResolvedValue({
    totalReserves: 10000.0,
    availableBalance: 10000.0,
    status: 'AVAILABLE'
  }),
  depositUSDCToPool: jest.fn(),
  withdrawUSDCFromPool: jest.fn(),
  transferUSDCToUser: jest.fn()
};

jest.mock('../../modules/usdc/usdc.service', () => mockUSDCService);

jest.mock('../../shared/blockchain/contractService', () => ({
  getContractUSDCBalance: jest.fn().mockResolvedValue('10000000000'), // 10,000 USDC
  getWalletAddress: jest.fn().mockReturnValue('******************************************'),
  isBlockchainConnected: jest.fn().mockReturnValue(true),
  transferUSDCToUser: jest.fn().mockResolvedValue({
    transactionHash: '0xabcdef123456789012345678901234567890abcdef123456789012345678901234',
    blockNumber: 12345,
    gasUsed: '21000',
    status: 1,
    success: true,
    to: '******************************************',
    amount: 100.5
  })
}));

// Mock constants globally
const mockConstants = {
  CENTRAL_WALLET_ID: 'central-mvt-wallet',
  TOKEN_TYPES: {
    MVT: 'MVT',
    USDC: 'USDC'
  },
  TRANSACTION_TYPES: {
    USDC_DEPOSIT: 'USDC_DEPOSIT',
    USDC_WITHDRAWAL: 'USDC_WITHDRAWAL'
  },
  TRANSACTION_STATUS: {
    COMPLETED: 'COMPLETED',
    PENDING: 'PENDING',
    FAILED: 'FAILED'
  },
  STATUS_CODES: {
    SUCCESS: 200,
    BAD_REQUEST: 400,
    UNAUTHORIZED: 401,
    FORBIDDEN: 403,
    NOT_FOUND: 404,
    INTERNAL_ERROR: 500
  }
};

// Make constants available globally
global.TOKEN_TYPES = mockConstants.TOKEN_TYPES;
global.TRANSACTION_TYPES = mockConstants.TRANSACTION_TYPES;
global.TRANSACTION_STATUS = mockConstants.TRANSACTION_STATUS;
global.STATUS_CODES = mockConstants.STATUS_CODES;
global.CENTRAL_WALLET_ID = mockConstants.CENTRAL_WALLET_ID;

jest.mock('../../shared/constants/index', () => mockConstants);

// Import modules after mocks are set up
const usdcHandlers = require('../../modules/usdc/usdc.handlers');
const usdcService = require('../../modules/usdc/usdc.service');
const authService = require('../../shared/services/authService');
const validationUtils = require('../../shared/utils/validationUtils');
const contractService = require('../../shared/blockchain/contractService');

// Set up global test utilities
global.testUtils = {
  // Mock GraphQL event
  createMockEvent: (cognitoIdentityId = 'admin-cognito-id', isAdmin = true) => ({
    requestContext: {
      identity: {
        cognitoIdentityId
      }
    },
    arguments: {},
    info: {
      fieldName: 'adminTransferUSDC'
    },
    source: {},
    stateValues: {},
    prev: null
  }),

  // Mock GraphQL arguments
  createMockArgs: (input = {}) => ({
    input
  }),

  // Mock user data
  createMockUser: (userId = 'test-user-123', walletAddress = '******************************************') => ({
    id: userId,
    cognitoId: 'test-cognito-id',
    email: '<EMAIL>',
    firstName: 'Test',
    lastName: 'User',
    walletAddress: walletAddress,
    role: 'MEMBER',
    isDeleted: 'false',
    createdAt: '2024-01-01T00:00:00.000Z',
    updatedAt: '2024-01-01T00:00:00.000Z'
  }),

  // Mock DynamoDB responses
  mockDynamoDBSuccess: (data = {}) => ({
    promise: jest.fn().mockResolvedValue(data)
  }),

  mockDynamoDBError: (error = new Error('DynamoDB error')) => ({
    promise: jest.fn().mockRejectedValue(error)
  })
};

describe('Admin USDC Transfer Scenario Tests', () => {
  beforeEach(() => {
    jest.clearAllMocks();

    // Mock environment variables
    process.env.ETHEREUM_RPC_URL = 'http://localhost:8545';
    process.env.PRIVATE_KEY = '******************************************123456789012345678901234';
    process.env.MVT_WITHDRAW_CONTRACT_ADDRESS = '******************************************';
    process.env.USDC_TOKEN_ADDRESS = '******************************************';

    // Setup default DynamoDB responses
    mockDynamoDB.getItem.mockReturnValue(global.testUtils.mockDynamoDBSuccess({
      Item: global.testUtils.createMockUser('test-user-123')
    }));
    mockDynamoDB.putItem.mockReturnValue(global.testUtils.mockDynamoDBSuccess());
    mockDynamoDB.updateItem.mockReturnValue(global.testUtils.mockDynamoDBSuccess());

    // Setup auth service defaults
    authService.checkAdminAuthorization.mockResolvedValue(true);
    authService.getCurrentUserDatabaseId.mockResolvedValue('admin-user-123');
    authService.getUserIdFromEvent.mockResolvedValue('admin-cognito-123');

    // Reset validation to pass by default
    validationUtils.validateUSDCTransferInput.mockReturnValue({ isValid: true });

    // Reset blockchain service to success by default
    contractService.transferUSDCToUser.mockResolvedValue({
      transactionHash: '0xabcdef123456789012345678901234567890abcdef123456789012345678901234',
      blockNumber: 12345,
      gasUsed: '21000',
      status: 1,
      success: true,
      to: '******************************************',
      amount: 100.5
    });
  });

  describe('Admin Transfer USDC to User', () => {
    test('should successfully transfer USDC from admin to user', async () => {
      // Arrange
      const userId = 'test-user-123';
      const amount = 100.5;
      const description = 'Test USDC transfer to user';
      const adminUserId = 'admin-user-123';

      const mockEvent = global.testUtils.createMockEvent('admin-cognito-id', true);
      const mockArgs = global.testUtils.createMockArgs({
        userId: userId,
        amount: amount,
        description: description
      });

      // Reset validation to pass for this test
      validationUtils.validateUSDCTransferInput.mockReturnValue({ isValid: true });

      // Mock user with wallet address
      const mockUser = global.testUtils.createMockUser(userId, '******************************************');
      mockDynamoDB.getItem.mockReturnValue(global.testUtils.mockDynamoDBSuccess({
        Item: mockUser
      }));

      // Mock USDC service methods
      mockUSDCService.getUSDCLiquidityPool.mockResolvedValue({
        totalReserves: 10000.0,
        availableBalance: 10000.0,
        status: 'AVAILABLE'
      });

      // Act
      const result = await usdcHandlers.handleAdminTransferUSDC(mockEvent, mockArgs);

      // Assert
      expect(result.statusCode).toBe(200);
      expect(result.message).toContain('Successfully transferred 100.5 USDC to user');
      expect(result.data).toBeDefined();
      expect(result.data.transactionType).toBe('USDC_WITHDRAWAL');
      expect(result.data.tokenType).toBe('USDC');
      expect(result.data.amount).toBe(amount);
      expect(result.data.toUserId).toBe(userId);
      expect(result.data.adminUserId).toBe(adminUserId);

      // Verify blockchain transfer was called
      expect(contractService.transferUSDCToUser).toHaveBeenCalledWith(
        mockUser.walletAddress,
        amount
      );
    });

    test('should fail when user does not have wallet address', async () => {
      // Arrange
      const userId = 'test-user-no-wallet';
      const amount = 50.0;

      const mockEvent = global.testUtils.createMockEvent('admin-cognito-id', true);
      const mockArgs = global.testUtils.createMockArgs({
        userId: userId,
        amount: amount,
        description: 'Test transfer'
      });

      // Mock user without wallet address
      const mockUser = global.testUtils.createMockUser(userId, null);
      mockDynamoDB.getItem.mockReturnValue(global.testUtils.mockDynamoDBSuccess({
        Item: mockUser
      }));

      // Act
      const result = await usdcHandlers.handleAdminTransferUSDC(mockEvent, mockArgs);

      // Assert
      expect(result.statusCode).toBe(500);
      expect(result.message).toContain('User does not have a wallet address configured');
    });

    test('should fail when user does not exist', async () => {
      // Arrange
      const userId = 'non-existent-user';
      const amount = 50.0;

      const mockEvent = global.testUtils.createMockEvent('admin-cognito-id', true);
      const mockArgs = global.testUtils.createMockArgs({
        userId: userId,
        amount: amount,
        description: 'Test transfer'
      });

      // Mock user not found
      mockDynamoDB.getItem.mockReturnValue(global.testUtils.mockDynamoDBSuccess({
        Item: null
      }));

      // Act
      const result = await usdcHandlers.handleAdminTransferUSDC(mockEvent, mockArgs);

      // Assert
      expect(result.statusCode).toBe(500);
      expect(result.message).toContain('User not found');
    });

    test('should fail when insufficient USDC in liquidity pool', async () => {
      // Arrange
      const userId = 'test-user-123';
      const amount = 15000.0; // More than available

      const mockEvent = global.testUtils.createMockEvent('admin-cognito-id', true);
      const mockArgs = global.testUtils.createMockArgs({
        userId: userId,
        amount: amount,
        description: 'Test large transfer'
      });

      // Mock user with wallet address
      const mockUser = global.testUtils.createMockUser(userId, '******************************************');
      mockDynamoDB.getItem.mockReturnValue(global.testUtils.mockDynamoDBSuccess({
        Item: mockUser
      }));

      // Mock insufficient liquidity pool - need to return lower balance than requested
      mockUSDCService.getUSDCLiquidityPool.mockResolvedValue({
        totalReserves: 1000.0,
        availableBalance: 1000.0, // Less than 15000
        status: 'AVAILABLE'
      });

      // Act
      const result = await usdcHandlers.handleAdminTransferUSDC(mockEvent, mockArgs);

      // Assert
      expect(result.statusCode).toBe(500);
      expect(result.message).toContain('Insufficient USDC in liquidity pool');
    });

    test('should fail with invalid amount', async () => {
      // Arrange
      const userId = 'test-user-123';
      const amount = -50.0; // Invalid negative amount

      const mockEvent = global.testUtils.createMockEvent('admin-cognito-id', true);
      const mockArgs = global.testUtils.createMockArgs({
        userId: userId,
        amount: amount,
        description: 'Test invalid amount'
      });

      // Mock validation failure
      validationUtils.validateUSDCTransferInput.mockReturnValue({
        isValid: false,
        error: 'Amount must be a positive number'
      });

      // Act
      const result = await usdcHandlers.handleAdminTransferUSDC(mockEvent, mockArgs);

      // Assert
      expect(result.statusCode).toBe(400);
      expect(result.message).toContain('Amount must be a positive number');
    });

    test('should fail when non-admin tries to transfer', async () => {
      // Arrange
      const userId = 'test-user-123';
      const amount = 50.0;

      const mockEvent = global.testUtils.createMockEvent('user-cognito-id', false);
      const mockArgs = global.testUtils.createMockArgs({
        userId: userId,
        amount: amount,
        description: 'Unauthorized transfer attempt'
      });

      // Mock non-admin authorization
      authService.checkAdminAuthorization.mockResolvedValue(false);

      // Act
      const result = await usdcHandlers.handleAdminTransferUSDC(mockEvent, mockArgs);

      // Assert - The actual status code returned is 401 for unauthorized
      expect(result.statusCode).toBe(401);
      expect(result.message).toContain('Unauthorized');
    });

    test('should handle blockchain transfer failure', async () => {
      // Arrange
      const userId = 'test-user-123';
      const amount = 100.0;

      const mockEvent = global.testUtils.createMockEvent('admin-cognito-id', true);
      const mockArgs = global.testUtils.createMockArgs({
        userId: userId,
        amount: amount,
        description: 'Test blockchain failure'
      });

      // Reset validation to pass for this test
      validationUtils.validateUSDCTransferInput.mockReturnValue({ isValid: true });

      // Mock user with wallet address
      const mockUser = global.testUtils.createMockUser(userId, '******************************************');
      mockDynamoDB.getItem.mockReturnValue(global.testUtils.mockDynamoDBSuccess({
        Item: mockUser
      }));

      // Mock sufficient liquidity pool
      jest.spyOn(usdcService, 'getUSDCLiquidityPool').mockResolvedValue({
        totalReserves: 10000.0,
        availableBalance: 10000.0,
        status: 'AVAILABLE'
      });

      // Mock blockchain failure
      contractService.transferUSDCToUser.mockRejectedValue(new Error('Blockchain transaction failed'));

      // Act
      const result = await usdcHandlers.handleAdminTransferUSDC(mockEvent, mockArgs);

      // Assert
      expect(result.statusCode).toBe(500);
      expect(result.message).toContain('Blockchain transaction failed');
    });
  });

  describe('Integration with Transaction Logging', () => {
    test('should create proper transaction record with all required fields', async () => {
      // Arrange
      const userId = 'test-user-123';
      const amount = 75.25;
      const description = 'Integration test transfer';
      const adminUserId = 'admin-user-123';

      const mockEvent = global.testUtils.createMockEvent('admin-cognito-id', true);
      const mockArgs = global.testUtils.createMockArgs({
        userId: userId,
        amount: amount,
        description: description
      });

      // Reset validation to pass for this test
      validationUtils.validateUSDCTransferInput.mockReturnValue({ isValid: true });

      // Mock user with wallet address
      const mockUser = global.testUtils.createMockUser(userId, '******************************************');
      mockDynamoDB.getItem.mockReturnValue(global.testUtils.mockDynamoDBSuccess({
        Item: mockUser
      }));

      // Mock USDC service
      jest.spyOn(usdcService, 'getUSDCLiquidityPool').mockResolvedValue({
        totalReserves: 10000.0,
        availableBalance: 10000.0,
        status: 'AVAILABLE'
      });

      // Reset blockchain mock to success for this test
      contractService.transferUSDCToUser.mockResolvedValue({
        transactionHash: '0xabcdef123456789012345678901234567890abcdef123456789012345678901234',
        blockNumber: 12345,
        gasUsed: '21000',
        status: 1,
        success: true,
        to: mockUser.walletAddress,
        amount: amount
      });

      // Act
      const result = await usdcHandlers.handleAdminTransferUSDC(mockEvent, mockArgs);

      // Assert successful response
      expect(result.statusCode).toBe(200);
      expect(result.data).toBeDefined();

      // Assert transaction record structure
      expect(result.data).toMatchObject({
        transactionType: 'USDC_WITHDRAWAL',
        tokenType: 'USDC',
        amount: amount,
        fromWalletId: 'usdc-liquidity-pool',
        toWalletId: `user-wallet-${userId}`,
        fromUserId: adminUserId,
        toUserId: userId,
        status: 'COMPLETED',
        adminUserId: adminUserId,
        description: description
      });

      // Verify transaction hash and blockchain data
      expect(result.data.transactionHash).toBeDefined();
      expect(result.data.blockNumber).toBeDefined();
      expect(result.data.gasUsed).toBeDefined();

      // Verify metadata
      expect(result.data.metadata).toBeDefined();
      const metadata = typeof result.data.metadata === 'string'
        ? JSON.parse(result.data.metadata)
        : result.data.metadata;
      expect(metadata.operation).toBe('usdc_transfer_to_user');
      expect(metadata.userWalletAddress).toBe(mockUser.walletAddress);
    });
  });
});
