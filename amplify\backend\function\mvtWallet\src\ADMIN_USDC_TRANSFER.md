# Admin USDC Transfer Functionality

## Overview

This document describes the new admin USDC transfer functionality that allows administrators to transfer USDC directly from the liquidity pool to user wallets. This feature follows the existing patterns established for MVT token transfers.

## Implementation

### Service Layer (`modules/usdc/usdc.service.js`)

**Function:** `transferUSDCToUser(userId, amount, adminUserId, description)`

- Validates USDC amount (supports float values)
- Retrieves user data and validates wallet address existence
- Checks liquidity pool balance for sufficient funds
- Executes blockchain transfer using `contractService.transferUSDCToUser()`
- Creates transaction record in MVTWalletTransaction table
- Returns complete transaction data

### Handler Layer (`modules/usdc/usdc.handlers.js`)

**Function:** `handleAdminTransferUSDC(event, args)`

- Enforces admin-only authorization
- Validates input using `validateUSDCTransferInput()`
- Calls service layer function
- Returns standardized response format

### Validation Layer (`shared/utils/validationUtils.js`)

**Function:** `validateUSDCTransferInput(input)`

- Validates required fields: `userId`, `amount`
- Validates optional field: `description`
- Uses existing USDC amount validation (supports floats)
- Uses existing user ID validation

## Usage

### Test Script

Run the comprehensive test suite:

```bash
npm run test:admin-usdc-transfer
```

Or run directly:

```bash
node test-admin-usdc-transfer.js
```

### GraphQL Integration (Future)

```graphql
mutation AdminTransferUSDC($input: AdminTransferUSDCInput!) {
  adminTransferUSDC(input: $input) {
    statusCode
    message
    data {
      id
      transactionType
      tokenType
      amount
      fromWalletId
      toWalletId
      fromUserId
      toUserId
      status
      transactionHash
      blockNumber
      gasUsed
      description
      adminUserId
      createdAt
    }
  }
}
```

**Input Variables:**
```json
{
  "input": {
    "userId": "user-123",
    "amount": 100.50,
    "description": "Admin USDC transfer to user"
  }
}
```

## Test Coverage

### Happy Path Tests
- ✅ Successful USDC transfer from admin to user
- ✅ Proper transaction logging and audit trail
- ✅ Blockchain interaction verification

### Error Handling Tests
- ✅ User without wallet address
- ✅ Non-existent user
- ✅ Insufficient liquidity pool balance
- ✅ Invalid amount validation
- ✅ Blockchain transaction failures

### Security Tests
- ✅ Non-admin authorization rejection
- ✅ Admin-only access enforcement

### Integration Tests
- ✅ Complete transaction record creation
- ✅ Proper metadata structure
- ✅ Database interaction verification

## Key Features

### 🔐 Security
- Admin-only access control
- Input validation and sanitization
- User existence and wallet validation

### 💰 Financial Integrity
- Liquidity pool balance verification
- Atomic transaction processing
- Complete audit trail

### 🔗 Blockchain Integration
- Direct USDC transfer to user wallet
- Transaction hash recording
- Gas usage tracking
- Block number logging

### 📊 Audit Trail
- Complete transaction logging
- Metadata preservation
- Admin action tracking
- User identification

## Transaction Flow

1. **Authorization Check** - Verify admin permissions
2. **Input Validation** - Validate userId, amount, description
3. **User Validation** - Check user exists and has wallet address
4. **Balance Check** - Verify sufficient USDC in liquidity pool
5. **Blockchain Transfer** - Execute USDC transfer to user wallet
6. **Transaction Logging** - Record complete transaction details
7. **Response** - Return transaction data to caller

## Database Schema

### MVTWalletTransaction Record
```javascript
{
  id: "usdc-transfer-{timestamp}-{random}",
  transactionType: "USDC_WITHDRAWAL",
  tokenType: "USDC",
  amount: 100.50,
  fromWalletId: "usdc-liquidity-pool",
  toWalletId: "user-wallet-{userId}",
  fromUserId: "{adminUserId}",
  toUserId: "{userId}",
  status: "COMPLETED",
  transactionHash: "0x...",
  blockNumber: 12345,
  gasUsed: 21000,
  description: "Admin transferred USDC to user",
  adminUserId: "{adminUserId}",
  metadata: {
    operation: "usdc_transfer_to_user",
    previousReserves: 10000.0,
    contractAddress: "0x...",
    userWalletAddress: "0x...",
    blockchainTx: "0x..."
  },
  createdAt: "2024-01-01T00:00:00.000Z",
  updatedAt: "2024-01-01T00:00:00.000Z"
}
```

## Error Handling

### Common Errors
- `User not found` - Invalid or non-existent userId
- `User does not have a wallet address configured` - User missing wallet
- `Insufficient USDC in liquidity pool` - Not enough funds
- `Amount must be a positive number` - Invalid amount
- `Unauthorized` - Non-admin access attempt
- `Blockchain transaction failed` - Contract interaction error

### Error Response Format
```javascript
{
  statusCode: 400|403|404|500,
  message: "Error description",
  error: "Detailed error message"
}
```

## Dependencies

### Required Services
- `contractService.transferUSDCToUser()` - Blockchain transfer
- `transactionService.createMVTWalletTransaction()` - Transaction logging
- `authService.checkAdminAuthorization()` - Admin verification

### Required Validations
- `validateUSDCTransferInput()` - Input validation
- `validateUSDCAmount()` - Amount validation
- `validateUserId()` - User ID validation

## Future Enhancements

1. **Batch Transfers** - Support multiple users in single operation
2. **Scheduled Transfers** - Time-based transfer execution
3. **Transfer Limits** - Daily/monthly transfer restrictions
4. **Approval Workflow** - Multi-admin approval for large amounts
5. **Notification System** - User notifications for received transfers

## Related Files

- `modules/usdc/usdc.service.js` - Service implementation
- `modules/usdc/usdc.handlers.js` - Handler implementation
- `shared/utils/validationUtils.js` - Validation functions
- `__tests__/scenarios/admin-usdc-transfer.test.js` - Test suite
- `test-admin-usdc-transfer.js` - Test runner script
